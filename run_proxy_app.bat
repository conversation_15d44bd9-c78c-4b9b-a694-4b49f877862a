@echo off
chcp 65001 >nul
cls

echo ===============================================
echo  🚀 AI 课程助手 - 流式代理服务器启动器
echo ===============================================
echo.

echo 📍 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到 Python，请先安装 Python 3.6+
    echo 📥 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python 环境检查通过

echo.
echo 📂 当前目录：%~dp0
echo 🔍 检查必要文件...

if not exist "index.html" (
    echo ❌ 错误：找不到 index.html 文件！
    pause
    exit /b 1
)

if not exist "proxy_server_streaming.py" (
    echo ❌ 错误：找不到 proxy_server_streaming.py 文件！
    pause
    exit /b 1
)

echo ✅ 文件检查通过
echo.

echo 🚀 启动 AI 课程助手流式代理服务器...
echo ✨ 新功能特性：
echo    • 🌊 实时流式AI响应
echo    • 📝 Markdown渲染支持  
echo    • 🔧 OpenAI SDK接口模式
echo    • 🖼️ 百度AI图片分析
echo    • 🤖 DeepSeek V3智能对话
echo.

echo ⚠️  注意：请不要关闭此命令行窗口
echo    关闭窗口将停止服务器运行
echo.

python proxy_server_streaming.py

echo.
echo 👋 感谢使用 AI 课程助手！
pause
