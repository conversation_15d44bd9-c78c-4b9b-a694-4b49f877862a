@echo off
chcp 65001 >nul
cls

echo ===============================================
echo  📦 AI 课程助手 - 依赖安装脚本
echo ===============================================
echo.

echo 📍 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到 Python，请先安装 Python 3.6+
    echo 📥 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python 环境检查通过
echo.

echo 🔍 检查 pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到 pip
    echo 🔧 请重新安装 Python 并确保包含 pip
    pause
    exit /b 1
)

echo ✅ pip 检查通过
echo.

echo 📂 当前目录：%~dp0
echo.

if exist "requirements.txt" (
    echo 📋 找到 requirements.txt 文件
    echo 🚀 开始安装依赖包...
    echo.
    
    pip install -r requirements.txt
    
    if errorlevel 1 (
        echo.
        echo ❌ 依赖安装失败！
        echo 🔧 请尝试手动安装：
        echo    pip install streamlit openai Pillow requests
        echo.
    ) else (
        echo.
        echo ✅ 所有依赖安装成功！
        echo.
        echo 📝 已安装的包：
        echo    • streamlit  - Web应用框架
        echo    • openai     - OpenAI API客户端  
        echo    • Pillow     - 图片处理库
        echo    • requests   - HTTP请求库（流式功能需要）
        echo.
        echo 🚀 现在可以运行应用了：
        echo    • run_proxy_app.bat      - 启动完整应用
        echo    • run_streaming_demo.bat - 流式功能演示
        echo.
    )
) else (
    echo ❌ 未找到 requirements.txt 文件
    echo 🔧 手动安装核心依赖：
    echo.
    
    echo 正在安装 requests（流式功能需要）...
    pip install requests
    
    if errorlevel 1 (
        echo ❌ requests 安装失败
    ) else (
        echo ✅ requests 安装成功
    )
    
    echo.
    echo 正在安装其他依赖...
    pip install streamlit openai Pillow
    
    echo.
)

echo 🎉 安装脚本执行完成！
echo.
pause
