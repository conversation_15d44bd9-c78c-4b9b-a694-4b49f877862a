#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI 课程助手 - 支持流式输出的代理服务器
支持百度 AI 图片分析和 DeepSeek 流式对话
"""

import http.server
import socketserver
import json
import urllib.request
import urllib.parse
import webbrowser
import os
import sys
from pathlib import Path
from urllib.error import HTTPError

# 检查并导入requests库
try:
    import requests
except ImportError:
    print("❌ 错误：未找到 requests 库")
    print("🔧 请运行以下命令安装：")
    print("   pip install requests")
    print("   或者: python -m pip install requests")
    print("\n📝 或者使用非流式模式的原始代理服务器：")
    print("   python proxy_server.py")
    sys.exit(1)

class StreamingProxyRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持流式输出的自定义请求处理器"""

    def do_OPTIONS(self):
        """处理 OPTIONS 请求（CORS 预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()

    def do_POST(self):
        """处理 POST 请求，包括 API 代理"""
        if self.path == '/api/baidu-ai':
            self.handle_baidu_ai_proxy()
        elif self.path == '/api/deepseek':
            self.handle_deepseek_proxy()
        else:
            super().do_POST()

    def handle_baidu_ai_proxy(self):
        """处理百度 AI 图片分析代理请求"""
        try:
            # 读取请求数据
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            request_data = json.loads(post_data.decode('utf-8'))

            print(f"🖼️ 收到百度AI请求，模型: {request_data.get('model', 'unknown')}")

            # 百度 AI API 配置
            baidu_api_key = "0621faa5641beacc5940d0fb978114a03a69b7eb"
            baidu_url = "https://aistudio.baidu.com/llm/lmapi/v3/chat/completions"

            # 构建请求
            headers = {
                'Authorization': f'Bearer {baidu_api_key}',
                'Content-Type': 'application/json',
            }

            # 转发请求到百度 AI
            req = urllib.request.Request(
                baidu_url,
                data=json.dumps(request_data).encode('utf-8'),
                headers=headers,
                method='POST'
            )

            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                response_data = json.loads(result)

            # 返回响应
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response_data).encode('utf-8'))

            print("✅ 百度AI请求处理完成")

        except HTTPError as e:
            self.send_error_response(f"百度 AI API 错误: {e.code} - {e.reason}")
        except Exception as e:
            self.send_error_response(f"代理服务器错误: {str(e)}")

    def handle_deepseek_proxy(self):
        """处理 DeepSeek API 代理请求 - 支持流式输出"""
        try:
            # 读取请求数据
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            request_data = json.loads(post_data.decode('utf-8'))

            print(f"🤖 收到DeepSeek请求，模型: {request_data.get('model', 'unknown')}")
            print(f"📡 流式模式: {request_data.get('stream', False)}")

            # OpenRouter API 配置
            openrouter_api_key = "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"
            openrouter_url = "https://openrouter.ai/api/v1/chat/completions"

            # 检查是否为流式请求
            is_stream = request_data.get('stream', False)

            if is_stream:
                # 处理流式请求
                self._handle_streaming_request(openrouter_url, openrouter_api_key, request_data)
            else:
                # 处理非流式请求（保持原有逻辑）
                self._handle_non_streaming_request(openrouter_url, openrouter_api_key, request_data)

        except HTTPError as e:
            self.send_error_response(f"DeepSeek API 错误: {e.code} - {e.reason}")
        except Exception as e:
            self.send_error_response(f"代理服务器错误: {str(e)}")

    def _handle_streaming_request(self, url, api_key, request_data):
        """处理流式请求"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
            }

            print("🔄 开始流式请求...")

            # 发送流式请求
            response = requests.post(
                url,
                headers=headers,
                json=request_data,
                stream=True,
                timeout=30
            )

            if response.status_code != 200:
                self.send_error_response(f"上游API错误: {response.status_code}")
                return

            # 设置SSE响应头
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'keep-alive')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
            self.end_headers()

            print("📡 开始流式传输...")

            # 流式转发响应
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    # 转发SSE数据
                    self.wfile.write(f"{line_str}\n".encode('utf-8'))
                    self.wfile.flush()

                    # 检查是否结束
                    if 'data: [DONE]' in line_str:
                        print("✅ 流式传输完成")
                        break

        except Exception as e:
            print(f"❌ 流式传输错误: {e}")
            self.send_error_response(f"流式传输错误: {str(e)}")

    def _handle_non_streaming_request(self, url, api_key, request_data):
        """处理非流式请求（原有逻辑）"""
        try:
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json',
            }

            # 转发请求到 OpenRouter
            req = urllib.request.Request(
                url,
                data=json.dumps(request_data).encode('utf-8'),
                headers=headers,
                method='POST'
            )

            with urllib.request.urlopen(req) as response:
                result = response.read().decode('utf-8')
                response_data = json.loads(result)

            # 返回响应
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response_data).encode('utf-8'))

            print("✅ DeepSeek请求处理完成")

        except Exception as e:
            print(f"❌ 非流式请求错误: {e}")
            self.send_error_response(f"非流式请求错误: {str(e)}")

    def send_error_response(self, error_message):
        """发送错误响应"""
        self.send_response(500)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        error_data = {
            "error": {
                "message": error_message,
                "type": "proxy_error"
            }
        }
        self.wfile.write(json.dumps(error_data).encode('utf-8'))

    def end_headers(self):
        """添加 CORS 头"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()

def start_streaming_server():
    """启动支持流式输出的代理服务器"""
    # 获取当前脚本目录
    current_dir = Path(__file__).parent
    os.chdir(current_dir)

    # 设置端口
    PORT = 8081

    # 检查 index.html 是否存在
    if not (current_dir / 'index.html').exists():
        print("❌ 错误：找不到 index.html 文件！")
        print(f"请确保 index.html 文件位于：{current_dir}")
        return

    try:
        with socketserver.TCPServer(("", PORT), StreamingProxyRequestHandler) as httpd:
            print("=" * 70)
            print("🚀 AI 课程助手流式代理服务器启动成功！")
            print("=" * 70)
            print(f"📍 本地地址：http://localhost:{PORT}")
            print(f"📍 网络地址：http://127.0.0.1:{PORT}")
            print("=" * 70)
            print("✨ 新功能特性：")
            print("   • 🌊 实时流式AI响应")
            print("   • 📝 Markdown渲染支持")
            print("   • 🔧 OpenAI SDK接口模式")
            print("   • 🖼️ 百度AI图片分析")
            print("   • 🤖 DeepSeek V3智能对话")
            print("=" * 70)
            print("🛠️ API 代理端点：")
            print("   • /api/baidu-ai - 百度AI图片分析")
            print("   • /api/deepseek - DeepSeek对话（支持流式）")
            print("=" * 70)

            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🌐 浏览器已自动打开")
            except Exception as e:
                print(f"⚠️ 无法自动打开浏览器：{e}")
                print(f"请手动打开浏览器并访问：http://localhost:{PORT}")

            print("\n✅ 流式代理服务器正在运行，等待连接...")
            print("   (按 Ctrl+C 停止服务器)")

            # 启动服务器
            httpd.serve_forever()

    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 48 or "Address already in use" in str(e):
            print(f"❌ 端口 {PORT} 已被占用，尝试使用其他端口...")
            # 尝试其他端口
            for port in range(8081, 8090):
                try:
                    with socketserver.TCPServer(("", port), StreamingProxyRequestHandler) as httpd:
                        print(f"✅ 使用端口 {port} 启动成功！")
                        webbrowser.open(f'http://localhost:{port}')
                        httpd.serve_forever()
                        break
                except OSError:
                    continue
        else:
            print(f"❌ 启动服务器时出错：{e}")
    except Exception as e:
        print(f"❌ 意外错误：{e}")

if __name__ == "__main__":
    start_streaming_server()
