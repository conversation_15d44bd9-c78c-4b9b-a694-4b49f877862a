<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">    <title>Your AI Course Copilot</title>
    <!-- Marked.js for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked@11.1.1/marked.min.js"></script>
    <!-- Highlight.js for code syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', sans-serif;
            background-color: #f8f9fa;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e1e5e9;
            padding: 20px;
            overflow-y: auto;
            flex-shrink: 0;
        }

        .sidebar h3 {
            color: #262730;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .new-chat-btn {
            width: 100%;
            padding: 10px;
            background: #ff4b4b;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .new-chat-btn:hover {
            background: #ff3333;
        }

        .conversation-item {
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            border: 1px solid transparent;
            word-wrap: break-word;
        }

        .conversation-item:hover {
            background: #f0f2f6;
        }

        .conversation-item.active {
            background: #e1f5fe;
            border-color: #0066cc;
        }

        .delete-btn {
            width: 100%;
            padding: 8px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 10px;
            font-size: 12px;
        }

        .delete-btn:hover {
            background: #c82333;
        }

        .image-status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
        }

        .image-status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .image-status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .image-preview {
            max-width: 150px;
            margin-top: 10px;
            border-radius: 8px;
        }

        .clear-image-btn {
            width: 100%;
            padding: 6px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 8px;
            font-size: 12px;
        }

        /* 主聊天区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: white;
            padding: 20px;
            border-bottom: 1px solid #e1e5e9;
        }

        .chat-header h1 {
            color: #262730;
            font-size: 24px;
            font-weight: 600;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 15px;
            border-radius: 15px;
            word-wrap: break-word;
            line-height: 1.4;
        }

        .message.user .message-content {
            background: #0066cc;
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.assistant .message-content {
            background: white;
            color: #262730;
            border: 1px solid #e1e5e9;
            border-bottom-left-radius: 5px;
        }

        /* Markdown Content Styling */
        .message-content h1, .message-content h2, .message-content h3,
        .message-content h4, .message-content h5, .message-content h6 {
            margin: 10px 0 5px 0;
            font-weight: bold;
        }

        .message-content h1 { font-size: 1.2em; }
        .message-content h2 { font-size: 1.1em; }
        .message-content h3 { font-size: 1.05em; }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 3px 0;
        }

        .message-content p {
            margin: 8px 0;
        }

        .message-content blockquote {
            border-left: 3px solid #ddd;
            margin: 8px 0;
            padding-left: 12px;
            color: #666;
            font-style: italic;
        }

        .message-content code {
            background: rgba(0,0,0,0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
        }

        .message-content pre {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 8px 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            line-height: 1.45;
        }

        .message-content pre code {
            background: none;
            padding: 0;
            border-radius: 0;
        }

        .message-content table {
            border-collapse: collapse;
            margin: 8px 0;
            width: 100%;
        }

        .message-content th, .message-content td {
            border: 1px solid #ddd;
            padding: 6px 8px;
            text-align: left;
        }

        .message-content th {
            background: #f6f8fa;
            font-weight: bold;
        }

        .message-content a {
            color: #0366d6;
            text-decoration: none;
        }

        .message-content a:hover {
            text-decoration: underline;
        }

        /* Streaming Animation */
        .streaming-cursor {
            display: inline-block;
            width: 2px;
            height: 1em;
            background: #007AFF;
            margin-left: 2px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .message.assistant .message-content {
            background: white;
            color: #262730;
            border: 1px solid #e1e5e9;
            border-bottom-left-radius: 5px;
        }

        .message-image {
            max-width: 300px;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        /* 输入区域 */
        .input-area {
            background: white;
            padding: 20px;
            border-top: 1px solid #e1e5e9;
            display: flex;
            align-items: flex-end;
            gap: 10px;
        }

        .camera-btn {
            padding: 12px;
            background: #ff4b4b;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            flex-shrink: 0;
        }

        .camera-btn:hover {
            background: #ff3333;
        }

        .message-input {
            flex: 1;
            padding: 12px 15px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
        }

        .send-btn {
            padding: 12px 20px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            flex-shrink: 0;
        }

        .send-btn:hover {
            background: #0052a3;
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 图片上传面板 */
        .upload-panel {
            position: fixed;
            bottom: 90px;
            left: 320px;
            right: 20px;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 1000;
            display: none;
        }

        .upload-panel h3 {
            margin-bottom: 15px;
            color: #262730;
        }

        .file-upload {
            border: 2px dashed #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            margin-bottom: 15px;
        }

        .file-upload:hover {
            border-color: #0066cc;
            background: #f8f9ff;
        }

        .file-upload.dragover {
            border-color: #0066cc;
            background: #e1f5fe;
        }

        .upload-actions {
            display: flex;
            gap: 10px;
        }

        .upload-actions button {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .confirm-btn {
            background: #28a745;
            color: white;
        }

        .cancel-btn {
            background: #6c757d;
            color: white;
        }

        .close-panel-btn {
            background: #dc3545;
            color: white;
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0066cc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-message {
            position: fixed;
            bottom: 120px;
            left: 320px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 999;
            display: none;
        }        .status-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-message.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .status-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 250px;
            }

            .upload-panel {
                left: 270px;
            }

            .status-message {
                left: 270px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <h3>💬 对话管理</h3>
            <button class="new-chat-btn" onclick="createNewConversation()">➕ 新建对话</button>

            <h4>📚 对话历史</h4>
            <div id="conversationList"></div>

            <button class="delete-btn" onclick="deleteCurrentConversation()" id="deleteBtn">🗑️ 删除当前对话</button>

            <hr style="margin: 20px 0; border: none; border-top: 1px solid #e1e5e9;">

            <h3>📷 图片状态</h3>
            <div id="imageStatus" class="image-status info">💬 纯文本模式</div>

            <details style="margin-top: 20px;">
                <summary style="cursor: pointer; font-weight: bold;">ℹ️ 使用说明</summary>
                <div style="margin-top: 10px; font-size: 12px; color: #666;">
                    <strong>功能介绍：</strong><br>
                    • 支持多个对话会话管理<br>
                    • 上传图片后，AI会先用百度AI分析<br>
                    • 结合DeepSeek V3生成智能回复<br>
                    • 支持PNG、JPG、JPEG、GIF格式<br><br>

                    <strong>使用步骤：</strong><br>
                    1. 点击输入框左侧📷按钮上传图片<br>
                    2. 在输入框输入问题<br>
                    3. 发送后AI会分析图片并回答
                </div>
            </details>
        </div>

        <!-- 主聊天区域 -->
        <div class="main-content">
            <div class="chat-header">
                <h1>Your AI course copilot</h1>
            </div>

            <div class="chat-messages" id="chatMessages"></div>

            <div class="input-area">
                <button class="camera-btn" onclick="toggleUploadPanel()" title="点击上传图片">📷</button>
                <textarea
                    class="message-input"
                    id="messageInput"
                    placeholder="Please enter your message"
                    onkeydown="handleKeyDown(event)"
                ></textarea>
                <button class="send-btn" onclick="sendMessage()" id="sendBtn">发送</button>
            </div>
        </div>
    </div>

    <!-- 图片上传面板 -->
    <div class="upload-panel" id="uploadPanel">
        <h3>📷 上传图片</h3>
        <div class="file-upload" id="fileUpload" onclick="document.getElementById('fileInput').click()">
            <div>点击选择图片文件或拖拽到此处</div>
            <div style="font-size: 12px; color: #666; margin-top: 5px;">支持 PNG、JPG、JPEG、GIF 格式</div>
        </div>
        <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">

        <div id="imagePreview" style="display: none; margin: 15px 0;">
            <img id="previewImage" style="max-width: 200px; border-radius: 8px;">
            <div id="imageInfo" style="font-size: 12px; color: #666; margin-top: 10px;"></div>
        </div>

        <div class="upload-actions">
            <button class="confirm-btn" onclick="confirmImage()" id="confirmBtn" style="display: none;">✅ 确认使用</button>
            <button class="cancel-btn" onclick="clearImage()">🗑️ 重新选择</button>
            <button class="close-panel-btn" onclick="toggleUploadPanel()">❌ 关闭面板</button>
        </div>
    </div>    <!-- 状态消息 -->
    <div class="status-message" id="statusMessage"></div>    <!-- 系统状态指示器 -->
    <div id="systemStatus" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 8px 12px; border-radius: 6px; font-size: 12px; z-index: 1000; display: none;">
        <div id="statusText">🔄 系统就绪</div>
        <div id="requestCount" style="font-size: 10px; opacity: 0.8;">活跃请求: 0</div>
    </div>

    <!-- 紧急重置按钮 -->
    <button id="emergencyResetBtn" onclick="emergencyReset()"
            style="position: fixed; bottom: 20px; right: 20px; background: #ff4444; color: white; border: none; padding: 10px 15px; border-radius: 6px; font-size: 12px; cursor: pointer; z-index: 1000; display: none;"
            title="如果系统卡住，点击重置状态">
        🚨 紧急重置
    </button>

    <script>        // 全局状态管理
        let conversations = {};
        let currentConversationId = 'conversation_1';
        let conversationCounter = 1;
        let uploadedImage = null;
        let isUploading = false;
        let activeRequestsCount = 0; // 添加活跃请求计数器
        let maxConcurrentRequests = 1; // 最大并发请求数
        let lastRequestTime = 0; // 记录最后请求时间
        let requestTimeoutId = null; // 请求超时定时器

        // API 配置
        const OPENROUTER_API_KEY = '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';
        const BAIDU_API_KEY = '0621faa5641beacc5940d0fb978114a03a69b7eb';

        // 状态监控配置
        const REQUEST_TIMEOUT = 120000; // 2分钟请求超时
        const HEALTH_CHECK_INTERVAL = 10000; // 10秒健康检查间隔        // 初始化应用
        function initApp() {
            // 初始化marked.js配置
            if (typeof marked !== 'undefined') {
                marked.setOptions({
                    highlight: function(code, lang) {
                        if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                            try {
                                return hljs.highlight(code, { language: lang }).value;
                            } catch (err) {}
                        }
                        return code;
                    },
                    breaks: true,
                    gfm: true
                });
                console.log("✅ Markdown渲染器初始化完成");
            }

            // 重置全局状态
            activeRequestsCount = 0;
            lastRequestTime = 0;
            console.log("🔄 重置活跃请求计数器为 0");

            // 创建默认对话
            conversations[currentConversationId] = {
                title: '新对话',
                messages: [],
                createdAt: new Date().toISOString()
            };

            updateConversationList();
            updateImageStatus();

            // 设置拖拽上传
            setupDragAndDrop();

            // 启动健康检查
            startHealthCheck();
            console.log("💓 健康检查系统已启动");

            // 添加调试信息显示（可选）
            if (window.location.search.includes('debug=true')) {
                console.log('🔧 调试模式已启用');
                setInterval(() => {
                    console.log(`📊 当前活跃请求数: ${activeRequestsCount}, 最大并发数: ${maxConcurrentRequests}`);
                    const sendBtn = document.getElementById('sendBtn');
                    if (sendBtn) {
                        console.log(`🔘 发送按钮状态: ${sendBtn.disabled ? '禁用' : '启用'}`);
                    }
                }, 5000);
            }

            console.log("🚀 应用初始化完成");
        }

        // 创建新对话
        function createNewConversation() {
            conversationCounter++;
            const newId = `conversation_${conversationCounter}`;
            conversations[newId] = {
                title: `新对话 ${conversationCounter}`,
                messages: [],
                createdAt: new Date().toISOString()
            };
            currentConversationId = newId;
            clearImage();
            updateConversationList();
            updateChatMessages();
            updateImageStatus();
        }

        // 切换对话
        function switchConversation(conversationId) {
            if (conversationId !== currentConversationId) {
                currentConversationId = conversationId;
                clearImage();
                updateConversationList();
                updateChatMessages();
                updateImageStatus();
            }
        }

        // 删除当前对话
        function deleteCurrentConversation() {
            if (Object.keys(conversations).length > 1) {
                delete conversations[currentConversationId];
                // 切换到第一个可用对话
                currentConversationId = Object.keys(conversations)[0];
                clearImage();
                updateConversationList();
                updateChatMessages();
                updateImageStatus();
            }
        }

        // 更新对话列表
        function updateConversationList() {
            const listElement = document.getElementById('conversationList');
            listElement.innerHTML = '';

            for (const [convId, convData] of Object.entries(conversations)) {
                const item = document.createElement('div');
                item.className = `conversation-item ${convId === currentConversationId ? 'active' : ''}`;

                let displayTitle;
                if (convData.messages.length > 0) {
                    const firstMessage = convData.messages[0].content;
                    displayTitle = firstMessage.length > 30 ? firstMessage.substring(0, 30) + '...' : firstMessage;
                } else {
                    displayTitle = convData.title;
                }

                item.innerHTML = convId === currentConversationId ?
                    `🔹 <strong>${displayTitle}</strong>` :
                    `📄 ${displayTitle}`;

                if (convId !== currentConversationId) {
                    item.onclick = () => switchConversation(convId);
                }

                listElement.appendChild(item);
            }

            // 更新删除按钮状态
            document.getElementById('deleteBtn').style.display =
                Object.keys(conversations).length > 1 ? 'block' : 'none';
        }

        // 更新聊天消息显示
        function updateChatMessages() {
            const messagesElement = document.getElementById('chatMessages');
            messagesElement.innerHTML = '';

            const currentMessages = conversations[currentConversationId].messages;
            for (const message of currentMessages) {
                addMessageToChat(message);
            }

            // 滚动到底部
            messagesElement.scrollTop = messagesElement.scrollHeight;
        }        // 添加消息到聊天界面
        function addMessageToChat(message, isStreaming = false) {
            const messagesElement = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${message.role}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            if (message.image) {
                const img = document.createElement('img');
                img.src = message.image;
                img.className = 'message-image';
                img.alt = '上传的图片';
                contentDiv.appendChild(img);
            }

            const textDiv = document.createElement('div');
            textDiv.className = 'message-text';

            // 根据消息类型处理内容渲染
            if (message.role === 'assistant' && typeof marked !== 'undefined') {
                // AI消息使用Markdown渲染
                try {
                    textDiv.innerHTML = marked.parse(message.content || '');
                    // 高亮代码块
                    if (typeof hljs !== 'undefined') {
                        textDiv.querySelectorAll('pre code').forEach((block) => {
                            hljs.highlightElement(block);
                        });
                    }
                } catch (error) {
                    console.warn('Markdown渲染失败，使用纯文本:', error);
                    textDiv.innerHTML = (message.content || '').replace(/\n/g, '<br>');
                }
            } else {
                // 用户消息使用纯文本
                textDiv.innerHTML = (message.content || '').replace(/\n/g, '<br>');
            }

            // 添加流式输出光标
            if (isStreaming && message.role === 'assistant') {
                const cursor = document.createElement('span');
                cursor.className = 'streaming-cursor';
                textDiv.appendChild(cursor);
            }

            contentDiv.appendChild(textDiv);
            messageDiv.appendChild(contentDiv);
            messagesElement.appendChild(messageDiv);

            // 滚动到底部
            messagesElement.scrollTop = messagesElement.scrollHeight;

            return messageDiv;
        }

        // 更新流式消息内容
        function updateStreamingMessage(messageDiv, content, isComplete = false) {
            const textDiv = messageDiv.querySelector('.message-text');
            if (!textDiv) return;

            // 移除旧的光标
            const oldCursor = textDiv.querySelector('.streaming-cursor');
            if (oldCursor) {
                oldCursor.remove();
            }

            // 渲染Markdown内容
            if (typeof marked !== 'undefined') {
                try {
                    textDiv.innerHTML = marked.parse(content || '');
                    // 高亮代码块
                    if (typeof hljs !== 'undefined') {
                        textDiv.querySelectorAll('pre code').forEach((block) => {
                            hljs.highlightElement(block);
                        });
                    }
                } catch (error) {
                    console.warn('Markdown渲染失败，使用纯文本:', error);
                    textDiv.innerHTML = (content || '').replace(/\n/g, '<br>');
                }
            } else {
                textDiv.innerHTML = (content || '').replace(/\n/g, '<br>');
            }

            // 如果未完成，添加光标
            if (!isComplete) {
                const cursor = document.createElement('span');
                cursor.className = 'streaming-cursor';
                textDiv.appendChild(cursor);
            }

            // 滚动到底部
            const messagesElement = document.getElementById('chatMessages');
            messagesElement.scrollTop = messagesElement.scrollHeight;
        }

        // 切换上传面板
        function toggleUploadPanel() {
            const panel = document.getElementById('uploadPanel');
            const isVisible = panel.style.display === 'block';
            panel.style.display = isVisible ? 'none' : 'block';

            if (!isVisible) {
                showStatusMessage('👆 请选择要上传的图片文件', 'info');
            } else {
                hideStatusMessage();
            }
        }

        // 处理文件选择
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                processImageFile(file);
            }
        }

        // 处理图片文件
        function processImageFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件！');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.getElementById('previewImage');
                img.src = e.target.result;

                const info = document.getElementById('imageInfo');
                info.innerHTML = `
                    <strong>图片信息：</strong><br>
                    文件名: ${file.name}<br>
                    文件大小: ${(file.size / 1024).toFixed(1)} KB<br>
                    文件类型: ${file.type}
                `;

                document.getElementById('imagePreview').style.display = 'block';
                document.getElementById('confirmBtn').style.display = 'block';

                // 临时存储图片数据
                uploadedImage = {
                    data: e.target.result,
                    file: file
                };
            };
            reader.readAsDataURL(file);
        }

        // 确认使用图片
        function confirmImage() {
            if (uploadedImage) {
                updateImageStatus();
                toggleUploadPanel();
                showStatusMessage('✅ 图片已准备，将随下一条消息发送', 'success');
            }
        }

        // 清除图片
        function clearImage() {
            uploadedImage = null;
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('confirmBtn').style.display = 'none';
            document.getElementById('fileInput').value = '';
            updateImageStatus();
            hideStatusMessage();
        }

        // 更新图片状态显示
        function updateImageStatus() {
            const statusElement = document.getElementById('imageStatus');

            if (uploadedImage) {
                statusElement.className = 'image-status success';
                statusElement.innerHTML = `
                    ✅ 图片已准备
                    <img src="${uploadedImage.data}" class="image-preview" alt="预览图片">
                    <button class="clear-image-btn" onclick="clearImage()">🗑️ 清除图片</button>
                `;
            } else {
                statusElement.className = 'image-status info';
                statusElement.innerHTML = '💬 纯文本模式';
            }
        }

        // 显示状态消息
        function showStatusMessage(message, type) {
            const statusMsg = document.getElementById('statusMessage');
            statusMsg.textContent = message;
            statusMsg.className = `status-message ${type}`;
            statusMsg.style.display = 'block';
        }

        // 隐藏状态消息
        function hideStatusMessage() {
            document.getElementById('statusMessage').style.display = 'none';
        }

        // 设置拖拽上传
        function setupDragAndDrop() {
            const uploadArea = document.getElementById('fileUpload');

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    processImageFile(files[0]);
                }
            });
        }        // 健康检查和自动恢复机制
        function startHealthCheck() {
            setInterval(() => {
                const now = Date.now();

                // 执行状态检查和自动恢复
                performStateCheck();

                // 更新状态指示器
                updateSystemStatus();

                // 健康状态日志
                if (window.location.search.includes('debug=true')) {
                    console.log(`💓 健康检查: 活跃请求=${activeRequestsCount}, 最后请求=${lastRequestTime ? Math.round((now-lastRequestTime)/1000) + 's前' : '无'}`);
                }
            }, HEALTH_CHECK_INTERVAL);
        }
          // 更新系统状态指示器
        function updateSystemStatus() {
            const statusElement = document.getElementById('systemStatus');
            const statusText = document.getElementById('statusText');
            const requestCount = document.getElementById('requestCount');
            const resetBtn = document.getElementById('emergencyResetBtn');

            if (!statusElement || !statusText || !requestCount) return;

            // 显示状态指示器（仅在调试模式或有活跃请求时）
            const showStatus = window.location.search.includes('debug=true') || activeRequestsCount > 0;
            statusElement.style.display = showStatus ? 'block' : 'none';

            // 显示紧急重置按钮（当有请求超过30秒或调试模式时）
            const now = Date.now();
            const showResetBtn = (activeRequestsCount > 0 && lastRequestTime > 0 && (now - lastRequestTime) > 30000) ||
                                window.location.search.includes('debug=true');
            if (resetBtn) {
                resetBtn.style.display = showResetBtn ? 'block' : 'none';
            }

            if (showStatus) {
                // 更新状态文本
                if (activeRequestsCount > 0) {
                    const timeSinceRequest = lastRequestTime > 0 ? Math.round((now - lastRequestTime) / 1000) : 0;
                    if (timeSinceRequest > 30) {
                        statusText.textContent = `⚠️ 请求可能卡住 (${timeSinceRequest}s)`;
                        statusElement.style.background = 'rgba(255, 0, 0, 0.9)'; // 红色
                    } else {
                        statusText.textContent = '🔄 AI正在处理...';
                        statusElement.style.background = 'rgba(255, 165, 0, 0.9)'; // 橙色
                    }
                } else {
                    statusText.textContent = '✅ 系统就绪';
                    statusElement.style.background = 'rgba(0, 128, 0, 0.9)'; // 绿色
                }

                // 更新请求计数
                requestCount.textContent = `活跃请求: ${activeRequestsCount}`;
            }
        }
          // 记录请求开始时间
        function markRequestStart() {
            lastRequestTime = Date.now();
            activeRequestsCount++;
            updateSystemStatus();
            console.log(`🚀 请求开始: 计数=${activeRequestsCount}, 时间=${new Date().toLocaleTimeString()}`);
        }

        // 记录请求结束时间
        function markRequestEnd() {
            activeRequestsCount = Math.max(0, activeRequestsCount - 1);
            if (activeRequestsCount === 0) {
                lastRequestTime = 0; // 清除时间记录
            }
            updateSystemStatus();
            console.log(`✅ 请求结束: 计数=${activeRequestsCount}`);
        }        // 紧急重置功能 - 用于解决卡住的状态（只重置UI，不影响对话历史）
        function emergencyReset() {
            console.warn("🚨 执行紧急重置...");

            // 重置所有状态计数器
            activeRequestsCount = 0;
            lastRequestTime = 0;

            // 清除可能的超时定时器
            if (requestTimeoutId) {
                clearTimeout(requestTimeoutId);
                requestTimeoutId = null;
            }

            // 重新启用UI
            const sendBtn = document.getElementById('sendBtn');
            const input = document.getElementById('messageInput');

            if (sendBtn) {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
                sendBtn.innerHTML = '发送'; // 确保移除loading动画
            }

            if (input) {
                input.disabled = false;
            }

            // 更新状态指示器
            updateSystemStatus();

            // 隐藏状态消息
            hideStatusMessage();

            // 重新渲染聊天消息，确保之前生成的内容不会丢失
            updateChatMessages();

            console.log("✅ 紧急重置完成，系统状态已恢复，对话历史已保留");
            showStatusMessage('🔄 UI状态已重置，对话内容已保留', 'info');

            // 3秒后隐藏提示消息
            setTimeout(() => {
                hideStatusMessage();
            }, 3000);
        }

        // 增强的状态检查和自动恢复
        function performStateCheck() {
            const sendBtn = document.getElementById('sendBtn');
            const input = document.getElementById('messageInput');

            // 检查UI状态是否与内部状态一致
            if (activeRequestsCount === 0) {
                // 如果没有活跃请求，但UI被禁用，则恢复UI
                if (sendBtn && sendBtn.disabled) {
                    console.warn('⚠️ 检测到UI状态不一致，自动恢复...');
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送';
                    sendBtn.innerHTML = '发送';
                }

                if (input && input.disabled) {
                    input.disabled = false;
                }
            }

            // 检查是否有僵尸请求
            const now = Date.now();
            if (activeRequestsCount > 0 && lastRequestTime > 0) {
                const timeSinceRequest = now - lastRequestTime;
                if (timeSinceRequest > REQUEST_TIMEOUT) {
                    console.warn(`⚠️ 检测到僵尸请求 (${Math.round(timeSinceRequest/1000)}秒)，执行自动恢复`);
                    emergencyReset();
                }
            }
        }

        // 添加键盘快捷键用于紧急重置（Ctrl+R）
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'r') {
                event.preventDefault();
                emergencyReset();
            }
        });

        // 处理键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }        // 发送消息 - 修复版本
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendBtn = document.getElementById('sendBtn');
            const message = input.value.trim();

            if (!message && !uploadedImage) return;

            // 检查并发请求限制
            if (activeRequestsCount >= maxConcurrentRequests) {
                showStatusMessage('⏳ 请等待当前请求完成后再发送', 'info');
                return;
            }

            // 获取发送按钮并检查是否已被禁用
            if (sendBtn.disabled) {
                console.warn('发送按钮已禁用，请等待当前请求完成');
                return;
            }

            // 标记请求开始
            markRequestStart();

            // 禁用发送按钮和输入框
            sendBtn.disabled = true;
            sendBtn.innerHTML = '<div class="loading"></div>';
            input.disabled = true;

            let success = false;
            let restoreInputValue = '';

            try {
                // 保存输入值用于失败时恢复
                restoreInputValue = message;

                // 添加用户消息
                const userMessage = {
                    role: 'user',
                    content: message || '请分析这张图片',
                    image: uploadedImage ? uploadedImage.data : null
                };

                conversations[currentConversationId].messages.push(userMessage);
                addMessageToChat(userMessage);

                // 清空输入和图片
                input.value = '';
                const currentImage = uploadedImage;
                clearImage();
                hideStatusMessage();                // 获取AI回复
                await getAIResponse(currentImage, message);
                success = true;
                console.log(`✅ 消息发送成功`);

            } catch (error) {
                console.error('发送消息出错:', error);
                showStatusMessage('❌ 发送消息失败，请重试', 'error');
                success = false;
            } finally {
                // 立即标记请求结束，防止健康检查误判
                console.log(`🔄 开始执行finally块，success=${success}`);
                
                // 确保请求状态被重置
                try {
                    markRequestEnd();
                    console.log(`✅ 请求状态已重置，当前活跃请求数: ${activeRequestsCount}`);
                } catch (e) {
                    console.warn('重置请求状态时出错:', e);
                    // 强制重置状态
                    activeRequestsCount = Math.max(0, activeRequestsCount - 1);
                    if (activeRequestsCount === 0) {
                        lastRequestTime = 0;
                    }
                    updateSystemStatus();
                }// 恢复UI状态
                try {
                    console.log(`🔄 开始恢复UI状态...成功状态: ${success}`);
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送';
                    sendBtn.innerHTML = '发送'; // 确保移除loading动画
                    input.disabled = false;

                    // 如果失败，恢复输入内容
                    if (!success && restoreInputValue) {
                        input.value = restoreInputValue;
                    }

                    // 重新聚焦到输入框
                    setTimeout(() => {
                        input.focus();
                    }, 100);

                    console.log(`✅ UI状态已恢复，发送按钮启用: ${!sendBtn.disabled}`);
                } catch (e) {
                    console.error('恢复UI状态时出错:', e);
                    // 如果UI恢复失败，执行紧急重置
                    setTimeout(() => {
                        emergencyReset();
                    }, 1000);
                }
            }
        }// 获取AI回复 - 支持流式输出（修复版本）
        async function getAIResponse(imageData, userMessage) {
            const messagesElement = document.getElementById('chatMessages');
            let loadingDiv = null;

            try {
                // 添加加载消息
                loadingDiv = document.createElement('div');
                loadingDiv.className = 'message assistant';
                loadingDiv.innerHTML = `
                    <div class="message-content">
                        ${imageData ? '🖼️ 正在使用百度AI分析图片...' : '🤖 AI正在思考...'}
                        <div class="loading" style="margin-left: 10px;"></div>
                    </div>
                `;
                messagesElement.appendChild(loadingDiv);
                messagesElement.scrollTop = messagesElement.scrollHeight;

                let finalMessage = userMessage;

                // 如果有图片，先用百度AI分析
                if (imageData) {
                    try {
                        const baiduAnalysis = await analyzeImageWithBaidu(imageData.data, userMessage);

                        // 更新加载消息
                        if (loadingDiv && loadingDiv.parentNode) {
                            loadingDiv.querySelector('.message-content').innerHTML = `
                                ✅ 图片分析完成，正在生成回复...
                                <div class="loading" style="margin-left: 10px;"></div>
                            `;
                        }

                        finalMessage = `用户问题：${userMessage}\n\n图片分析结果（来自百度AI）：${baiduAnalysis}\n\n请根据图片分析结果和用户问题，提供更深入的回答和建议。`;
                    } catch (imageError) {
                        console.error('图片分析失败:', imageError);
                        finalMessage = `用户问题：${userMessage}\n\n注意：图片分析失败（${imageError.message}），请仅根据用户问题回答。`;
                    }
                }

                // 构建消息历史
                const messages = conversations[currentConversationId].messages.map(m => ({
                    role: m.role,
                    content: m.role === 'user' && m === conversations[currentConversationId].messages[conversations[currentConversationId].messages.length - 1]
                        ? finalMessage
                        : m.content
                }));

                // 移除加载消息
                if (loadingDiv && loadingDiv.parentNode) {
                    try {
                        messagesElement.removeChild(loadingDiv);
                        loadingDiv = null;
                    } catch (e) {
                        console.warn('移除加载消息失败:', e);
                    }
                }

                // 开始流式AI回复
                await streamAIResponse(messages);

            } catch (error) {
                console.error('获取AI回复出错:', error);

                // 清理加载消息
                if (loadingDiv && loadingDiv.parentNode) {
                    try {
                        messagesElement.removeChild(loadingDiv);
                    } catch (e) {
                        console.warn('清理加载消息失败:', e);
                    }
                    loadingDiv = null;
                }

                // 添加错误消息
                const errorMessage = {
                    role: 'assistant',
                    content: `抱歉，获取回复时出现错误：${error.message}\n\n请稍后重试，或检查网络连接。`
                };

                try {
                    conversations[currentConversationId].messages.push(errorMessage);
                    addMessageToChat(errorMessage);
                } catch (e) {
                    console.error('添加错误消息失败:', e);
                }

                // 显示错误状态
                showStatusMessage('❌ AI回复失败，请重试', 'error');

                // 重新抛出错误，让调用者知道失败了
                throw error;
            }
        }        // 流式AI回复函数 - 修复版本
        async function streamAIResponse(messages) {
            const messagesElement = document.getElementById('chatMessages');
            let reader = null;
            let controller = null;
            let timeoutId = null;

            // 创建AI消息容器
            const assistantMessage = {
                role: 'assistant',
                content: ''
            };

            // 添加空的助手消息到界面
            const messageDiv = addMessageToChat(assistantMessage, true);

            try {
                // 创建AbortController用于取消请求
                controller = new AbortController();

                // 设置超时控制
                timeoutId = setTimeout(() => {
                    console.warn('请求超时，自动取消');
                    if (controller) {
                        controller.abort();
                    }
                }, 60000); // 60秒超时

                console.log("🚀 开始流式AI请求...");

                // 尝试使用流式输出
                const response = await fetch('/api/deepseek', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: 'deepseek/deepseek-chat-v3-0324:free',
                        messages: messages,
                        stream: true  // 启用流式输出
                    }),
                    signal: controller.signal
                });

                if (!response.ok) {
                    throw new Error(`流式API请求失败: ${response.status} - ${response.statusText}`);
                }

                console.log("✅ 流式响应连接建立");

                // 处理流式响应
                reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let lastUpdateTime = Date.now();
                let hasContent = false;

                try {
                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) {
                            console.log("✅ 流式传输完成");
                            break;
                        }

                        // 检查是否长时间无响应
                        const now = Date.now();
                        if (now - lastUpdateTime > 30000) { // 30秒无响应
                            console.warn('长时间无响应，终止连接');
                            break;
                        }
                        lastUpdateTime = now;

                        // 解码数据块
                        buffer += decoder.decode(value, { stream: true });
                        const lines = buffer.split('\n');

                        // 保留最后一个可能不完整的行
                        buffer = lines.pop() || '';

                        // 处理每一行
                        for (const line of lines) {
                            if (line.trim() === '' || !line.startsWith('data: ')) {
                                continue;
                            }

                            const data = line.slice(6); // 移除 'data: ' 前缀

                            if (data === '[DONE]') {
                                console.log("🔚 流式传输结束信号");
                                break;
                            }

                            try {
                                const parsed = JSON.parse(data);
                                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                                    assistantMessage.content += parsed.choices[0].delta.content;
                                    updateStreamingMessage(messageDiv, assistantMessage.content, false);
                                    hasContent = true;
                                }
                            } catch (e) {
                                console.warn('解析流式数据失败:', e, 'Data:', data);
                            }
                        }
                    }
                } finally {
                    // 确保reader正确关闭
                    if (reader) {
                        try {
                            await reader.cancel();
                        } catch (e) {
                            console.warn('关闭reader失败:', e);
                        }
                        reader = null;
                    }
                }

                // 检查是否有内容
                if (!hasContent || !assistantMessage.content.trim()) {
                    throw new Error('未收到有效的AI回复内容');
                }

                // 完成流式传输，移除光标
                updateStreamingMessage(messageDiv, assistantMessage.content, true);                // 保存到对话历史
                conversations[currentConversationId].messages.push(assistantMessage);

                console.log("✅ 流式AI回复完成，消息已保存到对话历史");
                return; // 明确返回，表示成功完成

            } catch (error) {
                console.warn('流式输出失败，尝试非流式模式:', error.name, error.message);

                // 如果不是用户取消的错误，尝试非流式模式
                if (error.name !== 'AbortError') {
                    try {
                        console.log("🔄 回退到非流式模式...");

                        // 创建新的控制器用于非流式请求
                        const fallbackController = new AbortController();
                        const fallbackTimeoutId = setTimeout(() => {
                            fallbackController.abort();
                        }, 30000); // 30秒超时

                        const response = await fetch('/api/deepseek', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                model: 'deepseek/deepseek-chat-v3-0324:free',
                                messages: messages,
                                stream: false  // 非流式模式
                            }),
                            signal: fallbackController.signal
                        });

                        clearTimeout(fallbackTimeoutId);

                        if (!response.ok) {
                            const errorData = await response.json().catch(() => ({}));
                            throw new Error(errorData.error?.message || `API请求失败: ${response.status}`);
                        }

                        const data = await response.json();
                        assistantMessage.content = data.choices[0].message.content;

                        // 更新消息内容
                        updateStreamingMessage(messageDiv, assistantMessage.content, true);                        // 保存到对话历史
                        conversations[currentConversationId].messages.push(assistantMessage);

                        console.log("✅ 非流式AI回复完成，消息已保存到对话历史");
                        return; // 明确返回，表示成功完成

                    } catch (fallbackError) {
                        console.error('非流式模式也失败:', fallbackError);
                        assistantMessage.content = '抱歉，获取回复时出现错误，请稍后重试。\n\n错误信息：' + fallbackError.message;
                        updateStreamingMessage(messageDiv, assistantMessage.content, true);
                        conversations[currentConversationId].messages.push(assistantMessage);

                        // 重新抛出错误让上层处理
                        throw fallbackError;
                    }
                } else {
                    // 用户取消的情况
                    console.log("⚠️ 用户取消了请求");
                    assistantMessage.content = '请求已取消';
                    updateStreamingMessage(messageDiv, assistantMessage.content, true);
                    conversations[currentConversationId].messages.push(assistantMessage);
                }            } finally {
                // 清理所有资源
                if (timeoutId) {
                    clearTimeout(timeoutId);
                    timeoutId = null;
                }

                if (reader) {
                    try {
                        await reader.cancel();
                    } catch (e) {
                        // 忽略取消错误
                    }
                    reader = null;
                }

                if (controller) {
                    try {
                        controller.abort();
                    } catch (e) {
                        // 忽略取消错误
                    }
                    controller = null;
                }                console.log("🧹 流式响应资源清理完成");
                
                // 移除过早的UI状态检查，让sendMessage的finally块处理UI恢复
                // 备用机制改为更长时间后执行，并且只在真正异常时才触发
                setTimeout(() => {
                    // 只有在请求计数器异常时才执行重置
                    if (activeRequestsCount > 0) {
                        const now = Date.now();
                        const timeSinceRequest = lastRequestTime > 0 ? now - lastRequestTime : 0;
                        if (timeSinceRequest > 10000) { // 10秒后还有活跃请求才认为异常
                            console.warn('🚨 检测到请求状态异常，执行备用恢复...');
                            emergencyReset();
                        }
                    }
                }, 10000); // 10秒后检查，给sendMessage充分时间恢复UI
            }
        }// 使用百度AI分析图片（通过代理服务器）
        async function analyzeImageWithBaidu(imageData, userPrompt) {
            try {
                const response = await fetch('/api/baidu-ai', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        model: 'ernie-4.5-turbo-vl-32k',
                        messages: [{
                            role: 'user',
                            content: [
                                {
                                    type: 'text',
                                    text: '请详细分析这张图片，提取图片中的所有元素、文字、对象、场景等信息，尽可能详细和准确地描述图片内容。'
                                },
                                {
                                    type: 'image_url',
                                    image_url: {
                                        url: imageData
                                    }
                                }
                            ]
                        }],
                        stream: false
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error?.message || `百度AI API请求失败: ${response.status}`);
                }

                const data = await response.json();
                return data.choices[0].message.content;

            } catch (error) {
                console.error('百度AI分析图片出错:', error);
                return `百度AI图片分析出错：${error.message}`;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔄 页面加载完成，开始初始化...');
            initApp();

            // 额外的状态检查，确保UI正确初始化
            setTimeout(() => {
                performStateCheck();
                console.log('✅ 初始化完成，系统就绪');
            }, 1000);
        });
    </script>
</body>
</html>
