# 🚀 AI课程助手 - 流式对话版

一个现代化的AI课程助手，支持实时流式响应、Markdown渲染和智能图片分析。

## ✨ 核心特性

- 🌊 **实时流式响应** - 首字符响应时间 ~100ms，即时显示AI思考过程
- 📝 **Markdown渲染** - 完整支持代码高亮、表格、列表等rich text
- 🤖 **双AI引擎** - DeepSeek V3对话 + 百度AI图片分析
- 🔧 **OpenAI兼容** - 标准OpenAI SDK接口模式

## 🚀 快速开始

### 一键启动
```batch
# 安装依赖并启动
install_dependencies.bat
run_proxy_app.bat
```

### 手动启动
```batch
pip install -r requirements.txt
python proxy_server_streaming.py
```

## ⚙️ 配置

编辑 `config.json` 文件：
```json
{
    "baidu": {
        "access_token": "你的百度AI访问令牌"
    },
    "deepseek": {
        "api_key": "你的DeepSeek API密钥"
    }
}
```

## 📁 项目结构
```
├── index.html                 # 主应用界面
├── proxy_server_streaming.py  # 流式代理服务器
├── config.json               # API配置文件
├── requirements.txt          # Python依赖
├── install_dependencies.bat  # 依赖安装脚本
├── run_proxy_app.bat        # 应用启动脚本
└── README.md                # 项目说明
```

## 🛠️ 技术特性

- **流式处理**: Server-Sent Events (SSE) 实现实时响应
- **Markdown**: marked.js + highlight.js 提供rich text支持
- **响应式设计**: 现代化界面，适配各种屏幕
- **智能回退**: 网络问题时自动切换到非流式模式

## 💬 使用说明

1. **文本对话**: 输入问题，享受实时流式AI响应
2. **图片分析**: 上传图片，获得AI视觉分析结果
3. **会话管理**: 创建多个对话，保持上下文记忆

## 🔍 故障排除

**无法启动服务器**
```batch
python --version  # 检查Python版本
pip install -r requirements.txt  # 重新安装依赖
```

**流式响应不工作**
- 检查网络连接和API密钥配置
- 查看浏览器控制台错误信息
- 系统会自动回退到非流式模式

---
**🌟 享受流式AI对话的极致体验！**